/* 共享变量定义 */
:root {
    /* === 通知 === */
    --color-success: #10B981;
    --color-error: #EF4444;
    
    /* === 字体 === */
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    
    /* === 圆角 === */
    --radius-xs: 2px;
    --radius-s: 4px;
    --radius-m: 8px;
    --radius-l: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;
}

/* 暗色主题（默认）*/
.theme-dark {
    /* === 品牌色 === */
    --color-brand: #2269EC;
    --color-brand-hover: #0B51D3;
  
    /* === 辅助色 === */
    --color-support: rgba(255, 255, 255, 0.12);
    --color-support-hover: rgba(255, 255, 255, 0.06);
  
    /* === 边框 === */
    --color-border: rgba(255, 255, 255, 0.12);
    --color-divider: #000000;
  
    /* === 内容颜色 === */
    --color-content-accent: rgba(255, 255, 255, 0.9);
    --color-content-regular: rgba(255, 255, 255, 0.7);
    --color-content-secondary: rgba(255, 255, 255, 0.3);
    --color-content-mute: rgba(255, 255, 255, 0.35);
    --color-content-invert: #FFFFFF;
  
    /* === 背景 === */
    --color-bg-page: #1A1A1A;
    --color-bg-dialog: #1F1F1F;
    --color-bg-primary: #272727;
    --color-bg-overlay: rgba(255, 255, 255, 0.05);
    --color-bg-input: rgba(255, 255, 255, 0.07);
    --color-bg-hover: rgba(255, 255, 255, 0.05);
}

/* 亮色主题 */
.theme-light {
    /* === 品牌色 === */
    --color-brand: #2269EC;
    --color-brand-hover: #0B51D3;
  
    /* === 辅助色 === */
    --color-support: rgba(0, 0, 0, 0.08);
    --color-support-hover: rgba(0, 0, 0, 0.04);
  
    /* === 边框 === */
    --color-border: rgba(0, 0, 0, 0.12);
    --color-divider: #E0E0E0;
  
    /* === 内容颜色 === */
    --color-content-accent: rgba(0, 0, 0, 0.9);
    --color-content-regular: rgba(0, 0, 0, 0.7);
    --color-content-secondary: rgba(0, 0, 0, 0.4);
    --color-content-mute: rgba(0, 0, 0, 0.35);
    --color-content-invert: #000000;
  
    /* === 背景 === */
    --color-bg-page: #FFFFFF;
    --color-bg-dialog: #F8F8F8;
    --color-bg-primary: #F0F0F0;
    --color-bg-overlay: rgba(0, 0, 0, 0.03);
    --color-bg-input: rgba(0, 0, 0, 0.05);
}
