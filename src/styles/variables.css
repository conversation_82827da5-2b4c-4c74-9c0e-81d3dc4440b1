/* 共享变量定义 */
:root {
    /* === 通知 === */
    --color-success: #008e5e;
    --color-error: #EF4444;
    
    /* === 阴影 === */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
    --shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    
    /* === 字体 === */
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    
    /* === 圆角 === */
    --radius-xs: 2px;
    --radius-s: 4px;
    --radius-m: 8px;
    --radius-l: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;
}

/* 暗色主题（默认）*/
.theme-dark {
    /* === 品牌色 === */
    --color-brand: #2269EC;
    --color-brand-hover: #0B51D3;
  
    /* === 辅助色 === */
    --color-support: rgba(255, 255, 255, 0.12);
    --color-support-hover: rgba(255, 255, 255, 0.06);
  
    /* === 边框 === */
    --color-border: rgba(255, 255, 255, 0.12);
    --color-divider: #000000;
  
    /* === 内容颜色 === */
    --color-content-accent: rgba(255, 255, 255, 0.9);
    --color-content-regular: rgba(255, 255, 255, 0.7);
    --color-content-secondary: rgba(255, 255, 255, 0.3);
    --color-content-mute: rgba(255, 255, 255, 0.35);
    --color-content-invert: #FFFFFF;
  
    /* === 背景 === */
    --color-bg-page: #1A1A1A;
    --color-bg-dialog: #1F1F1F;
    --color-bg-primary: #272727;
    --color-bg-overlay: rgba(255, 255, 255, 0.05);
    --color-bg-input: rgba(255, 255, 255, 0.07);
    --color-bg-hover: rgba(255, 255, 255, 0.05);
}
