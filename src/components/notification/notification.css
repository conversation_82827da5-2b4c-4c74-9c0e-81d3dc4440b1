.notification-container {
  position: fixed;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column-reverse;
  gap: 8px;
  z-index: 2000;
  align-items: flex-start;
}

.notification {
  padding: 10px 16px;
  border-radius: var(--radius-m);
  color: #FFFFFF; /* 固定为白色，确保在任何背景下都清晰可见 */
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  animation: fadeInOut 3s ease-in-out;
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.notification.success { background: var(--color-success); }
.notification.error { background: var(--color-error); }
.notification.info { background: var(--color-brand); }

@keyframes fadeInOut {
  0%, 100% { opacity: 0; transform: translateY(20px); }
  10%, 90% { opacity: 1; transform: translateY(0); }
}
