.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 2000;
}

.notification {
  padding: 10px 16px;
  border-radius: var(--radius-m);
  color: #fff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  animation: fadeInOut 3s ease-in-out;
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.notification.success { background: var(--color-success); }
.notification.error { background: var(--color-error); }
.notification.info { background: var(--color-brand); }

@keyframes fadeInOut {
  0%, 100% { opacity: 0; transform: translateY(-10px); }
  10%, 90% { opacity: 1; transform: translateY(0); }
}
