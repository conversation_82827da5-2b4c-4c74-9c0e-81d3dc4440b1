import { useState, useRef, useEffect, Suspense } from 'react';
import { useNotification } from '../components/notification/notification';
import './RenderPage.css';
import { SearchBox } from '../components/search-box/search-box';
import { PrimaryButton } from '../components/primary-button/primary-button';
import { SecondaryButton } from '../components/secondary-button/secondary-button';
import { DropDown } from '../components/drop-down/drop-down';
import { TabGroup } from '../components/tab-group/tab-group';
import { TabItem } from '../components/tab-item/tab-item';
import { CustomMaterialPanel } from '../components/custom-material-panel/custom-material-panel';
import { Copy, Download, Upload, HelpCircle, RotateCcw, Box, Paintbrush } from 'lucide-react';
import { Canvas } from '@react-three/fiber';
import type { ThreeEvent } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF, Stage, Html } from '@react-three/drei';
import * as THREE from 'three';
import MaterialThumbnail from '../components/material-thumbnail/material-thumbnail';
import { UploadModelModal } from '../components/upload-model-modal/upload-model-modal';

import { apiService } from '../services/api';
import type { ModelData, MaterialData } from '../services/api';

interface MaterialProps {
  color: string;
  metalness: number; // 0-1
  roughness: number; // 0-1
  opacity: number;   // 0-1, 1 为不透明
}

// 每个 Mesh 名称 -> 对应材质
type PerMeshMaterials = Record<string, MaterialProps>;

interface ModelProps {
  modelPath: string;
  materialProps: MaterialProps;
  perMeshMaterials: PerMeshMaterials;
  onMeshClick: (meshName: string, event: React.MouseEvent) => void;
  applyGlobalMaterial: boolean;
  selectedMeshName: string | null;
}

const Model = ({ modelPath, materialProps, perMeshMaterials, onMeshClick, applyGlobalMaterial, selectedMeshName }: ModelProps) => {
  const { scene } = useGLTF(modelPath);
  
  useEffect(() => {
    // 重置场景位置和旋转
    scene.position.set(0, 0, 0);
    scene.rotation.set(0, 0, 0);
    scene.scale.set(1, 1, 1);
    
    // 计算场景的包围盒
    const box = new THREE.Box3().setFromObject(scene);
    const center = new THREE.Vector3();
    box.getCenter(center);
    
    // 将场景原点移动到几何中心
    scene.position.x = -center.x;
    scene.position.y = -center.y;
    scene.position.z = -center.z;
    
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        // 应用材质
        if (applyGlobalMaterial) {
          const newMat = new THREE.MeshStandardMaterial({
            color: new THREE.Color(materialProps.color),
            metalness: materialProps.metalness,
            roughness: materialProps.roughness,
            transparent: materialProps.opacity < 1,
            opacity: materialProps.opacity,
          });
          object.material = newMat;
        } else if (Object.keys(perMeshMaterials).length === 0) {
          // 如果没有设置过任何部分材质，使用全局材质
          const newMat = new THREE.MeshStandardMaterial({
            color: new THREE.Color(materialProps.color),
            metalness: materialProps.metalness,
            roughness: materialProps.roughness,
            transparent: materialProps.opacity < 1,
            opacity: materialProps.opacity,
          });
          object.material = newMat;
        } else if (perMeshMaterials[object.name]) {
          const mat = perMeshMaterials[object.name];
          const newMat = new THREE.MeshStandardMaterial({
            color: new THREE.Color(mat.color),
            metalness: mat.metalness,
            roughness: mat.roughness,
            transparent: mat.opacity < 1,
            opacity: mat.opacity,
          });
          object.material = newMat;
        }
        const isSelected = object.name === selectedMeshName;
        // 边框高亮
        const existingOutline = object.getObjectByName('outline');
        if (isSelected && !existingOutline) {
          const geo = new THREE.EdgesGeometry((object as THREE.Mesh).geometry);
          const matLine = new THREE.LineBasicMaterial({ color: '#FFD54F' });
          const outline = new THREE.LineSegments(geo, matLine);
          outline.name = 'outline';
          object.add(outline);
        }
        if (!isSelected && existingOutline) {
          object.remove(existingOutline);
        }
      }
    });
  }, [scene, materialProps, perMeshMaterials, applyGlobalMaterial, selectedMeshName]);

  return (
    <group>
      <primitive 
        object={scene} 
        onPointerDown={(e: ThreeEvent<PointerEvent>) => {
          const clickedMesh = e.object as THREE.Object3D;
          onMeshClick(clickedMesh.name, e as unknown as React.MouseEvent);
        }} 
      />
    </group>
  )
}

const Loader = () => {
  return (
    <Html center>
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <div className="loading-text">加载中...</div>
      </div>
    </Html>
  );
};

interface RenderPageProps {
  theme?: 'light' | 'dark';
}

const RenderPage = ({ theme = 'dark' }: RenderPageProps) => {
  // 初始为空，等后台数据返回后自动赋值
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('会通材料');
  const [materialSettings, setMaterialSettings] = useState<MaterialProps>({
    color: '#B39B9C',
    metalness: 0.5,
    roughness: 0.5,
    opacity: 1
  });
  
  // 存储模型列表和选中的模型数据
  const [models, setModels] = useState<ModelData[]>([]);
  const [currentModel, setCurrentModel] = useState<ModelData | null>(null);
  // 存储上传的模型URL
  const [uploadedModelUrl, setUploadedModelUrl] = useState<string | null>(null);
  
  // 存储材质列表和选中的材质数据
  const [materials, setMaterials] = useState<MaterialData[]>([]);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);
  
  // 显示加载状态和后台管理入口相关逻辑
  const showLoader = loading && (!currentModel || !currentModel.filePath);
  const [logoClickCount, setLogoClickCount] = useState(0);
  const logoClickTimer = useRef<number | null>(null);
  

  // 当前选中的 mesh 名称
  const [selectedMeshName, setSelectedMeshName] = useState<string | null>(null);
  // 每个 mesh 对应的材质设置
  const [perMeshMaterials, setPerMeshMaterials] = useState<PerMeshMaterials>({});
  const [perMeshMaterialIds, setPerMeshMaterialIds] = useState<Record<string,string>>({});
  const [applyGlobalMaterial, setApplyGlobalMaterial] = useState<boolean>(false);

  // 获取所有已应用材质的ID（包含重复）
  const appliedMaterialIds = Object.values(perMeshMaterialIds);
  // 获取当前选中的材质ID（备用）
  
  // 处理模型文件上传
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const handleModelUpload = (file: File) => {
    // 检查文件类型
    const fileType = file.name.split('.').pop()?.toLowerCase() || '';
    if (!['glb', 'gltf'].includes(fileType)) {
      alert('请上传 GLB 或 GLTF 格式的模型文件');
      return;
    }
    const url = URL.createObjectURL(file);
    setUploadedModelUrl(url);
    const tempModel: ModelData = {
      id: 'uploaded-model',
      name: file.name,
      filePath: url,
      thumbnail: '',
      fileType,
      size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
      createdAt: new Date().toISOString()
    };
    setCurrentModel(tempModel);
    setSelectedMeshName(null);
    setPerMeshMaterials({});
    setPerMeshMaterialIds({});
    setUploadModalVisible(false);
  };

  // 处理整体模型的材质应用
  const applyMaterialToModel = (material: MaterialData) => {
    const newSettings = {
      color: material.color,
      metalness: material.metalness / 100,
      roughness: material.roughness / 100,
      opacity: material.glass ? (100 - material.glass) / 100 : 1
    };
    
    // 如果当前有选中的部分，只更新该部分
    if (selectedMeshName) {
      setPerMeshMaterials(prev => ({
        ...prev,
        [selectedMeshName]: newSettings
      }));
      setPerMeshMaterialIds(prev => ({
        ...prev,
        [selectedMeshName]: material.id
      }));
    } else {
      // 如果没有选中的部分，应用全局材质
      setMaterialSettings(newSettings);
      setApplyGlobalMaterial(true);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // 获取模型数据
        const modelData = await apiService.getModels();
        setModels(modelData);

        // 如果当前没有选中的模型，自动选中第一个
        if (!selectedModel && modelData.length > 0) {
          setSelectedModel(modelData[0].id);
          setCurrentModel(modelData[0]);
        } else {
          // 否则根据当前选中的id查找
          const found = modelData.find(m => m.id === selectedModel);
          setCurrentModel(found || null);
        }

        // 获取材质数据
        const materialData = await apiService.getMaterials();
        setMaterials(materialData);
        // 设置初始材质设置（如果有材质）
        if (materialData.length > 0) {
          setMaterialSettings({
            color: materialData[0].color,
            metalness: materialData[0].metalness / 100,
            roughness: materialData[0].roughness / 100,
            opacity: materialData[0].glass ? (100 - materialData[0].glass) / 100 : 1
          });
        }
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    // 只在 selectedModel 变化时重新同步 currentModel
  }, [selectedModel]);
  
  // 当选择模型变化时更新当前模型数据
  useEffect(() => {
    console.log('models 数据:', models);
    console.log('当前选中的模型 ID:', selectedModel);
    
    if (!selectedModel || models.length === 0) {
      setCurrentModel(null);
      return;
    }
    const model = models.find(m => m.id === selectedModel);
    console.log('找到的模型:', model);
    setCurrentModel(model || null);
  }, [selectedModel, models]);
  

  const handleLogoClick = () => {
    const newCount = logoClickCount + 1;
    setLogoClickCount(newCount);
    
    // 清除之前的计时器
    if (logoClickTimer.current) {
      window.clearTimeout(logoClickTimer.current);
    }
    
    // 设置新的计时器，2秒内未达到3次点击则重置计数
    logoClickTimer.current = window.setTimeout(() => {
      setLogoClickCount(0);
    }, 2000);
    
    // 如果达到3次点击，跳转到后台登录页面
    if (newCount === 3) {
      setLogoClickCount(0);
      if (logoClickTimer.current) {
        window.clearTimeout(logoClickTimer.current);
      }
      window.location.href = '/admin';
    }
  };

  // -------------- 新增：Canvas 引用及复制 / 保存功能 --------------
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const notify = useNotification();

  const handleCopyImage = async () => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      // 定义 Clipboard 接口的类型声明
      interface ExtendedClipboard extends Clipboard {
        write(data: ClipboardItem[]): Promise<void>;
      }
      
      interface ClipboardItemConstructor {
        new (items: Record<string, Blob>): ClipboardItem;
      }
      
      canvas.toBlob(async (blob) => {
        if (!blob) return;
        
        try {
          // 使用类型断言处理实验性 API
          const clipboard = navigator.clipboard as unknown as ExtendedClipboard;
          const ClipboardItem = window.ClipboardItem as unknown as ClipboardItemConstructor;
          
          if (!ClipboardItem) {
            throw new Error('Clipboard API 不支持 ClipboardItem');
          }
          
          const item = new ClipboardItem({ 'image/png': blob });
          await clipboard.write([item]);
          notify('图片已复制到剪贴板', 'success');
        } catch (err) {
          console.error('复制图片失败', err);
          notify('复制图片失败，请重试', 'error');
        }
      }, 'image/png');
    } catch (error) {
      console.error('复制失败:', error);
      notify('复制失败，请检查浏览器权限', 'error');
    }
  };

  // 保存当前渲染结果到本地
  const handleSaveImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    canvas.toBlob((blob) => {
      if (!blob) return;
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'render.png';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 'image/png');
  };
  // --------------------------------------------------------------

  return (
    <div className="render-page">

      <div className="title-bar">
        <img className="logo" src="images/Logo.png" alt="RINKO" onClick={handleLogoClick}/>
        <div className="user-controls">
          <SecondaryButton icon={Copy} onClick={handleCopyImage}>复制图片</SecondaryButton>
          <PrimaryButton icon={Download} onClick={handleSaveImage}>保存图片</PrimaryButton>
        </div>
      </div>

      <div className="render-container">
        <div className="render-window">
          {/* 3D渲染区域 */}
          <div className="render-area">
            <Canvas 
              onCreated={({ gl }) => { canvasRef.current = gl.domElement; }}
              gl={{ preserveDrawingBuffer: true, alpha: true }}
              onClick={(e) => {
                // 点击空白处取消选中
                const target = e.target as HTMLElement;
                if (target.className === 'canvas') {
                  setSelectedMeshName(null);
                }
              }}
              shadows camera={{ position: [0, 0, 3], fov: 50 }} style={{ background: 'transparent' }}>
              <fog attach="fog" args={[theme === 'light' ? '#F5F5F5' : '#1A1A1A', 5, 20]} />
              {showLoader ? (
                <Loader />
              ) : (
                <Suspense fallback={<Loader />}>
                  {currentModel && (uploadedModelUrl || currentModel.filePath) ? (
                    <Stage environment="city" intensity={0.6} adjustCamera={false} shadows={false} preset="rembrandt" scale={1.5}>
                      <Model 
                        modelPath={uploadedModelUrl || currentModel.filePath || ''}
                        materialProps={materialSettings}
                        perMeshMaterials={perMeshMaterials}
                        onMeshClick={(name, event) => {
                          event.stopPropagation(); // 阻止事件冒泡到 Canvas
                          setSelectedMeshName(name); // 直接设置为新选中的部分
                        }}
                        applyGlobalMaterial={applyGlobalMaterial}
                        selectedMeshName={selectedMeshName}
                      />
                    </Stage>
                  ) : (
                    <Html center>
                      <div style={{
                        color: '#fff', 
                        textAlign: 'center',
                        padding: '20px',
                        maxWidth: '300px'
                      }}>
                        <div>未选择模型或模型数据缺失</div>
                        <div style={{marginTop: '10px', fontSize: '0.9em', opacity: 0.8}}>
                          请从左侧选择模型或点击"上传模型"按钮
                        </div>
                      </div>
                    </Html>
                  )}
                  <Environment preset="city" />
                </Suspense>
              )}
              <OrbitControls makeDefault enablePan={true} enableZoom={true} enableRotate={true} />
            </Canvas>
          </div>
          
          <div className="button-container">
            <div className="control-button">
              <div className="icon-wrapper">
                <RotateCcw size={16} />
              </div>
              <span>默认视图</span>
            </div>
            <div className="control-button">
              <div className="icon-wrapper">
                <HelpCircle size={16} />
              </div>
              <span>操作说明</span>
            </div>
          </div>
        </div>
        
        {/* 属性面板 */}
        <div className="property-panel">
          <div className="panel-section">
            <div className="section-header">
              <div className="icon-wrapper">
                <Box size={16} />
              </div>
              <span>模型</span>
            </div>
            <div className="dropdown-wrapper">
              <DropDown
                options={models.map(model => ({ value: model.id, label: model.name }))}
                value={selectedModel}
                onChange={(value) => {
                  console.log('下拉框选择变化:', value);
                  setSelectedModel(value as string);
                }}
                placeholder={models.length === 0 ? '暂无模型' : ''}
              />
            </div>
            <div className="upload-button-wrapper">
              <SecondaryButton 
                icon={Upload} 
                fullWidth
                onClick={() => setUploadModalVisible(true)}
              >
                上传模型
              </SecondaryButton>
            </div>
          </div>
          
          <div className="panel-section" style={{ flex: 1 }}>
            <div className="section-header">
              <div className="icon-wrapper">
                <Paintbrush size={16} />
              </div>
              <span>材质设置</span>
            </div>
            
            <div className="material-thumbnails">
              {appliedMaterialIds.length > 0 ? (
                appliedMaterialIds.map((materialId, index) => {
                  const material = materials.find(m => m.id === materialId);
                  if (!material) return null;
                  
                  // 获取所有使用此材质的mesh名称
                  const meshNames = Object.entries(perMeshMaterialIds)
                    .filter(([, mid]) => mid === materialId)
                    .map(([name]) => name);
                  
                  // 只有当有选中的mesh时才高亮对应的材质
                  const isActive = selectedMeshName !== null && 
                    meshNames.includes(selectedMeshName);
                  
                  return (
                    <MaterialThumbnail
                      key={`${materialId}-${index}`}
                      material={material}
                      active={isActive}
                      onClick={() => {
                        // 如果当前有选中的mesh，切换到下一个使用此材质的mesh
                        if (meshNames.length > 0) {
                          // 如果当前选中的mesh使用此材质，则切换到下一个
                          const currentIndex = meshNames.indexOf(selectedMeshName || '');
                          const nextIndex = (currentIndex + 1) % meshNames.length;
                          setSelectedMeshName(meshNames[nextIndex]);
                        }
                      }}
                    />
                  );
                })
              ) : (
                <div className="no-materials">
                  暂无应用材质
                </div>
              )}
            </div>
            
            <TabGroup 
              className="tab-switch"
              gap={4}
              defaultActiveIndex={activeTab === '会通材料' ? 0 : 1}
              onChange={(index) => setActiveTab(index === 0 ? '会通材料' : '自定义')}
            >
              <TabItem label="会通材料" className="tab-item" />
              <TabItem label="自定义" className="tab-item" />
            </TabGroup>
            
            <div className="materials-container">
              {activeTab === '会通材料' ? (
                <>
                  <div className="search-wrapper">
                    <SearchBox 
                      placeholder="搜索" 
                      value={searchQuery} 
                      onChange={(value) => setSearchQuery(value)} 
                      onSearch={() => console.log('搜索:', searchQuery)}
                    />
                  </div>
                  
                  <div className="materials-grid">
                    {materials
                      .filter(material => material.name.includes(searchQuery))
                      .map((material) => (
                        <MaterialThumbnail
                          key={material.id}
                          material={material}
                          active={selectedMeshName ? perMeshMaterialIds[selectedMeshName] === material.id : false}
                          onClick={() => {
                            // 应用材质到选中部分或整个模型
                            applyMaterialToModel(material);
                          }}
                        />
                      ))}
                  </div>
                </>
              ) : (
                <CustomMaterialPanel
                  defaultSettings={materialSettings}
                  onChange={(settings) => {
                    setMaterialSettings(settings as unknown as MaterialProps);
                    console.log('材质设置更新:', settings);
                    // Three.js 会自动响应材质属性变化并重新渲染
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
      {uploadModalVisible && (
        <UploadModelModal 
          visible={uploadModalVisible}
          onClose={() => setUploadModalVisible(false)}
          onUpload={handleModelUpload}
        />
      )}
    </div>
  );
};

export default RenderPage;
